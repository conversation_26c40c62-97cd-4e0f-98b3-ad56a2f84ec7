import { NextRequest, NextResponse } from 'next/server';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';

export async function POST(request: NextRequest) {
  try {
    const { username, password } = await request.json();
    
    const adminUsername = process.env.ADMIN_USERNAME || 'admin';
    const adminPassword = process.env.ADMIN_PASSWORD || 'admin123';
    const jwtSecret = process.env.JWT_SECRET || 'default-secret';
    
    if (!username || !password) {
      return NextResponse.json({ error: '用户名和密码不能为空' }, { status: 400 });
    }
    
    if (username !== adminUsername) {
      return NextResponse.json({ error: '用户名或密码错误' }, { status: 401 });
    }
    
    // 简单密码验证（生产环境应该使用哈希密码）
    if (password !== adminPassword) {
      return NextResponse.json({ error: '用户名或密码错误' }, { status: 401 });
    }
    
    // 生成JWT token
    const token = jwt.sign(
      { username: adminUsername, role: 'admin' },
      jwtSecret,
      { expiresIn: '24h' }
    );
    
    // 设置HttpOnly cookie
    const response = NextResponse.json({
      success: true,
      message: '登录成功'
    });
    
    response.cookies.set('admin-token', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 24 * 60 * 60 * 1000, // 24小时
      path: '/'
    });
    
    return response;
    
  } catch (error) {
    console.error('登录API错误:', error);
    return NextResponse.json(
      { error: '登录失败，请稍后重试' }, 
      { status: 500 }
    );
  }
} 