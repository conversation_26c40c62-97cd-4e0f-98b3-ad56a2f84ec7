import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { imageGenerations } from '@/lib/schema';
import { sql, count, and, gte, lt } from 'drizzle-orm';
import jwt from 'jsonwebtoken';

// 验证管理员token
function verifyAdminToken(request: NextRequest) {
  const token = request.cookies.get('admin-token')?.value;
  if (!token) {
    return false;
  }
  
  try {
    const jwtSecret = process.env.JWT_SECRET || 'default-secret';
    jwt.verify(token, jwtSecret);
    return true;
  } catch {
    return false;
  }
}

export async function GET(request: NextRequest) {
  try {
    // 验证管理员权限
    if (!verifyAdminToken(request)) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 });
    }
    
    // 获取总生图数量
    const totalCount = await db
      .select({ count: count() })
      .from(imageGenerations);
    
    // 获取最近30天的每日统计
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    // 创建日期范围
    const dailyStats = [];
    for (let i = 29; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      const startOfDay = new Date(date.setHours(0, 0, 0, 0));
      const endOfDay = new Date(date.setHours(23, 59, 59, 999));
      
      const dayCount = await db
        .select({ count: count() })
        .from(imageGenerations)
        .where(
          and(
            gte(imageGenerations.requestTime, startOfDay),
            lt(imageGenerations.requestTime, endOfDay)
          )
        );
      
      dailyStats.push({
        date: startOfDay.toISOString().split('T')[0],
        count: dayCount[0]?.count || 0
      });
    }
    
    // 获取模型使用统计
    const modelStats = await db
      .select({
        model: imageGenerations.model,
        count: count()
      })
      .from(imageGenerations)
      .groupBy(imageGenerations.model);
    
    return NextResponse.json({
      success: true,
      data: {
        totalCount: totalCount[0]?.count || 0,
        dailyStats,
        modelStats
      }
    });
    
  } catch (error) {
    console.error('统计API错误:', error);
    return NextResponse.json(
      { error: '获取统计数据失败' }, 
      { status: 500 }
    );
  }
} 