# 数据库驱动迁移说明

## 从 better-sqlite3 迁移到 @libsql/client

### 迁移原因
- **Windows编译问题**: `better-sqlite3` 在Windows上需要编译二进制文件，经常出现权限和工具链问题
- **跨平台兼容性**: `@libsql/client` 是纯JavaScript实现，无需编译
- **现代化**: LibSQL是SQLite的现代化分支，具有更好的性能和功能

### 已完成的更改

#### 1. 依赖包更换
```bash
# 移除
pnpm remove @types/better-sqlite3

# 添加
pnpm add @libsql/client
```

#### 2. 配置文件更新

**`drizzle.config.ts`**:
```typescript
export default {
  schema: './lib/schema.ts',
  out: './drizzle',
  dialect: 'turso', // 改为 turso (LibSQL的云版本)
  dbCredentials: {
    url: process.env.DATABASE_URL || `file:${process.cwd()}/sqlite.db`,
  },
} satisfies Config;
```

**`lib/db.ts`**:
```typescript
import { drizzle } from 'drizzle-orm/libsql';
import { createClient } from '@libsql/client';
import * as schema from './schema';
import path from 'path';

const dbPath = process.env.DATABASE_URL || `file:${path.resolve(process.cwd(), 'sqlite.db')}`;

const client = createClient({
  url: dbPath,
});

export const db = drizzle(client, { schema });
```

#### 3. 初始化脚本更新

**`scripts/init-db.js`**:
```javascript
const { createClient } = require('@libsql/client');

async function initDatabase() {
  const client = createClient({
    url: process.env.DATABASE_URL || 'file:./sqlite.db',
  });
  
  // 使用 await client.execute() 替代 db.exec()
  await client.execute(createTableSQL);
  await client.close();
}
```

### 兼容性说明

#### ✅ 保持兼容
- **数据格式**: 完全兼容SQLite数据库文件
- **SQL语法**: 100%兼容SQLite语法
- **Drizzle ORM**: 完全支持现有的schema定义
- **功能特性**: 所有现有功能保持不变

#### 🔄 API变化
- **同步 → 异步**: 数据库操作现在都是异步的
- **连接方式**: 使用 `createClient()` 替代 `new Database()`
- **URL格式**: 使用 `file:` 协议前缀

### 运行验证

#### 1. 数据库初始化
```bash
node scripts/init-db.js
```

#### 2. 启动开发服务器
```bash
pnpm dev
```

#### 3. 测试生图功能
访问 http://localhost:3000 并测试图片生成功能

### 环境变量配置

可选的环境变量配置 `.env.local`:
```env
# 数据库配置 (可选，默认使用本地文件)
DATABASE_URL=file:./sqlite.db

# 或者使用Turso云数据库
# DATABASE_URL=libsql://your-database.turso.io
# DATABASE_AUTH_TOKEN=your-auth-token
```

### 故障排除

#### Q: 数据库连接错误？
A: 确保数据库文件路径正确，使用绝对路径：
```typescript
const dbPath = `file:${path.resolve(process.cwd(), 'sqlite.db')}`;
```

#### Q: 权限问题？
A: 检查数据库文件和目录的读写权限

#### Q: 想使用云数据库？
A: 可以配置Turso云数据库URL，完全兼容

### 总结

✅ **成功解决了Windows编译问题**  
✅ **保持了完全的SQLite兼容性**  
✅ **改进了跨平台支持**  
✅ **为未来扩展到云数据库做好准备**

现在项目可以在任何平台上无需编译直接运行！ 