"use client"

import type React from "react"

import { useState, useRef, useEffect } from "react"
import type { Image as ImageType } from "@/types/image"
import { X, Copy, Download, ZoomIn, ZoomOut } from "lucide-react"
import Image from "next/image"
import { toast } from "sonner"

interface ImagePreviewProps {
  image: ImageType
  onClose: () => void
}

export default function ImagePreview({ image, onClose }: ImagePreviewProps) {
  const [scale, setScale] = useState(1)
  const [position, setPosition] = useState({ x: 0, y: 0 })
  const [isDragging, setIsDragging] = useState(false)
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 })
  const imageRef = useRef<HTMLDivElement>(null)

  const handleZoomIn = () => {
    setScale((prev) => Math.min(prev + 0.25, 3))
  }

  const handleZoomOut = () => {
    setScale((prev) => Math.max(prev - 0.25, 0.5))
  }

  const handleWheel = (e: React.WheelEvent) => {
    e.preventDefault()
    if (e.deltaY < 0) {
      handleZoomIn()
    } else {
      handleZoomOut()
    }
  }

  const handleMouseDown = (e: React.MouseEvent) => {
    setIsDragging(true)
    setDragStart({ x: e.clientX - position.x, y: e.clientY - position.y })
  }

  const handleMouseMove = (e: React.MouseEvent) => {
    if (isDragging) {
      setPosition({
        x: e.clientX - dragStart.x,
        y: e.clientY - dragStart.y,
      })
    }
  }

  const handleMouseUp = () => {
    setIsDragging(false)
  }

  const handleCopyPrompt = () => {
    navigator.clipboard.writeText(image.prompt)
    const promptLines = image.prompt.split('\n')
    const displayedPrompt = promptLines.length > 3 
      ? promptLines.slice(0, 3).join('\n') + '...' 
      : image.prompt
    toast.success(`复制 " ${displayedPrompt} " 成功`)
  }

  const handleDownload = () => {
    // 实际应用中，这里会下载图片
    console.log("Downloading image:", image.url)
  }

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "Escape") {
        onClose()
      }
    }

    window.addEventListener("keydown", handleKeyDown)
    return () => window.removeEventListener("keydown", handleKeyDown)
  }, [onClose])

  return (
    <div className="fixed inset-0 bg-black bg-opacity-80 z-50 flex items-center justify-center">
      <div className="absolute top-4 right-4 z-10">
        <button onClick={onClose} className="bg-white bg-opacity-20 hover:bg-opacity-30 rounded-full p-2">
          <X className="h-6 w-6 text-white" />
        </button>
      </div>

      <div className="absolute top-4 left-1/2 transform -translate-x-1/2 z-10 flex space-x-2">
        <button
          onClick={handleDownload}
          className="bg-white bg-opacity-20 hover:bg-opacity-30 rounded-full p-2"
          title="下载"
        >
          <Download className="h-5 w-5 text-white" />
        </button>
        <button
          onClick={handleZoomIn}
          className="bg-white bg-opacity-20 hover:bg-opacity-30 rounded-full p-2"
          title="放大"
        >
          <ZoomIn className="h-5 w-5 text-white" />
        </button>
        <button
          onClick={handleZoomOut}
          className="bg-white bg-opacity-20 hover:bg-opacity-30 rounded-full p-2"
          title="缩小"
        >
          <ZoomOut className="h-5 w-5 text-white" />
        </button>
      </div>

      <div
        className="w-full h-full flex items-center justify-center overflow-hidden"
        onWheel={handleWheel}
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
      >
        <div
          ref={imageRef}
          className="relative cursor-move"
          style={{
            transform: `translate(${position.x}px, ${position.y}px) scale(${scale})`,
            transition: isDragging ? "none" : "transform 0.2s",
          }}
        >
          <Image
            src={image.url || "/placeholder.svg"}
            alt={image.prompt}
            width={600}
            height={800}
            className="max-h-[80vh] w-auto"
          />
        </div>
      </div>

      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-10 max-w-2xl w-full mx-4">
        <div className="bg-gray-900 bg-opacity-80 rounded-lg p-4 text-white">
          <p className="text-sm line-clamp-2 mb-2">
            {image.prompt ||
              "一个穿着冬季外套和毛线帽的男孩在宁静的松树林中仰望红色灯笼，阳光透过树木洒落，氛围宁静祥和，吉卜力风格动漫背景"}
          </p>
          <div className="flex justify-between items-center">
            <button
              onClick={handleCopyPrompt}
              className="bg-[#f26d21] text-white rounded-md px-3 py-1 text-sm flex items-center"
            >
              <Copy className="h-4 w-4 mr-1" />
              复制提示词
            </button>
            <div className="flex text-xs text-gray-300">
              <span className="mr-4">模型: {image.model}</span>
              <span>生成时间: {new Date(image.createdAt).getHours()}秒</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
