"use client"

import { useState } from "react"
import type { Image as ImageType } from "@/types/image"
import { Trash2 } from "lucide-react"
import Image from "next/image"

interface ImageGridProps {
  images: ImageType[]
  onImageClick: (image: ImageType) => void
  onDeleteImage: (id: string) => void
}

export default function ImageGrid({ images, onImageClick, onDeleteImage }: ImageGridProps) {
  const [hoveredImageId, setHoveredImageId] = useState<string | null>(null)

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return `${date.getHours()}秒`
  }

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
      {images.map((image) => (
        <div
          key={image.id}
          className="relative aspect-[3/4] bg-gray-200 rounded-lg overflow-hidden cursor-pointer"
          onMouseEnter={() => setHoveredImageId(image.id)}
          onMouseLeave={() => setHoveredImageId(null)}
          onClick={() => onImageClick(image)}
        >
          <Image src={image.url || "/placeholder.svg"} alt={image.prompt} fill className="object-cover" />

          {hoveredImageId === image.id && (
            <>
              <div className="absolute top-2 right-2 z-10">
                <button
                  onClick={(e) => {
                    e.stopPropagation()
                    onDeleteImage(image.id)
                  }}
                  className="bg-gray-800 bg-opacity-70 text-white p-2 rounded-full hover:bg-opacity-100"
                >
                  <Trash2 className="h-4 w-4" />
                </button>
              </div>

              <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-3">
                <div className="flex items-center justify-between text-white text-sm">
                  <span>{image.model}</span>
                  <span>{formatDate(image.createdAt)}</span>
                </div>
              </div>
            </>
          )}
        </div>
      ))}
    </div>
  )
}
