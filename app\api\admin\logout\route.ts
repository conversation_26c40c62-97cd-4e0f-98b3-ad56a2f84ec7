import { NextResponse } from 'next/server';

export async function POST() {
  try {
    const response = NextResponse.json({
      success: true,
      message: '退出登录成功'
    });
    
    // 删除HttpOnly cookie
    response.cookies.set('admin-token', '', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 0, // 立即过期
      path: '/'
    });
    
    return response;
    
  } catch (error) {
    console.error('退出登录API错误:', error);
    return NextResponse.json(
      { error: '退出登录失败' }, 
      { status: 500 }
    );
  }
} 