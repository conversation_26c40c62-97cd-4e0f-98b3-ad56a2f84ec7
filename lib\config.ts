// 生图配置参数
export const IMAGE_GENERATION_CONFIG = {
  // 固定参数
  fixedParams: {
    nologo: 'true',
    private: 'true',
    enhance: 'true',
    safe: 'true',
    referrer: 'freeai.yooart.top',
  },
  
  // 动态参数（从环境变量获取）
  getDynamicParams: () => ({
    ...(process.env.POLLINATIONS_TOKEN && { token: process.env.POLLINATIONS_TOKEN }),
  }),
  
  // 尺寸比例配置
  aspectRatios: [
    {
      value: '1:1',
      label: '1:1 (正方形)',
      width: 1024,
      height: 1024,
    },
    {
      value: '3:2',
      label: '3:2 (横屏)',
      width: 900,
      height: 600,
    },
    {
      value: '2:3',
      label: '2:3 (竖屏)',
      width: 600,
      height: 900,
    },
  ],
  
  // 可用模型
  models: [
    { value: 'flux', label: 'Flux' },
    { value: 'turbo', label: 'Turbo' },
    { value: 'stabilityai', label: 'Stability AI' },
  ],
} as const;

// 生成随机种子
export function generateRandomSeed(): number {
  return Math.floor(Math.random() * 1000000);
}

// 根据比例获取尺寸
export function getDimensionsByRatio(ratio: string): { width: number; height: number } {
  const ratioConfig = IMAGE_GENERATION_CONFIG.aspectRatios.find(r => r.value === ratio);
  if (!ratioConfig) {
    // 默认返回1:1比例
    return { width: 1024, height: 1024 };
  }
  return { width: ratioConfig.width, height: ratioConfig.height };
}

// 构建完整的图片生成URL
export function buildImageUrl(prompt: string, params: {
  model: string;
  seed: number;
  width: number;
  height: number;
}): string {
  const baseUrl = 'https://image.pollinations.ai/prompt';
  const encodedPrompt = encodeURIComponent(prompt);
  
  const searchParams = new URLSearchParams({
    model: params.model,
    seed: params.seed.toString(),
    width: params.width.toString(),
    height: params.height.toString(),
    ...IMAGE_GENERATION_CONFIG.fixedParams,
    ...IMAGE_GENERATION_CONFIG.getDynamicParams(),
  });
  
  return `${baseUrl}/${encodedPrompt}?${searchParams.toString()}`;
} 