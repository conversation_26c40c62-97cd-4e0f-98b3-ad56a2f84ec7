# YOOART FREE - AI图片生成服务

一个基于Next.js和Pollinations.AI的免费AI图片生成平台，支持多种生图模型和管理后台。

## 功能特点

### 🎨 前端功能
- **智能生图**: 支持多种AI模型（Flux、Turbo、Stability AI）
- **实时预览**: 即时查看生成的图片
- **本地存储**: 使用IndexedDB保存生图历史
- **响应式设计**: 适配各种设备尺寸

### 🔧 后端功能
- **Pollinations.AI集成**: 直接调用优质AI生图服务
- **数据统计**: 记录生图请求和性能数据
- **SQLite存储**: 轻量级数据库，无需额外配置

### 👨‍💼 管理后台
- **数据仪表板**: 可视化生图统计和趋势分析
- **创作记录**: 查看所有用户的生图历史
- **安全登录**: JWT认证保护管理员访问

## 技术栈

- **前端**: Next.js 15, React 19, TypeScript, Tailwind CSS
- **后端**: Next.js API Routes, Drizzle ORM
- **数据库**: SQLite + LibSQL Client
- **UI组件**: Radix UI, shadcn/ui
- **图表**: Recharts
- **认证**: JWT + HttpOnly Cookies

## 快速开始

### 1. 环境要求
- Node.js 18+ 
- pnpm (推荐) 或 npm

### 2. 安装依赖
\`\`\`bash
# 克隆项目
git clone <repository-url>
cd yooart-freeai

# 安装依赖
pnpm install

# 注意：已使用 @libsql/client 替代 better-sqlite3，无需编译
\`\`\`

### 3. 环境变量配置
创建 \`.env.local\` 文件：
\`\`\`env
# 管理员账号配置
ADMIN_USERNAME=admin
ADMIN_PASSWORD=admin123

# JWT 密钥配置
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production

# 数据库配置
DATABASE_URL=./sqlite.db

# Pollinations.AI API Token (可选)
POLLINATIONS_TOKEN=YOUR_TOKEN
\`\`\`

### 4. 数据库初始化
\`\`\`bash
# 生成数据库迁移文件
npx drizzle-kit generate

# 执行迁移（如果失败，可以手动运行初始化脚本）
npx drizzle-kit migrate

# 或者使用手动初始化脚本
node scripts/init-db.js
\`\`\`

### 5. 启动开发服务器
\`\`\`bash
pnpm dev
\`\`\`

访问 http://localhost:3000 查看前台页面
访问 http://localhost:3000/admin/login 进入管理后台

## 项目结构

\`\`\`
yooart-freeai/
├── app/                    # Next.js App Router
│   ├── api/               # API路由
│   │   ├── generate/      # 生图API
│   │   └── admin/         # 管理后台API
│   ├── admin/             # 管理后台页面
│   └── page.tsx           # 前台首页
├── components/            # React组件
│   ├── ui/               # 基础UI组件
│   ├── admin-layout.tsx  # 管理后台布局
│   └── prompt-input.tsx  # 生图输入组件
├── lib/                   # 工具库
│   ├── db.ts             # 数据库连接
│   ├── schema.ts         # 数据库模式
│   ├── indexeddb.ts      # 前端本地存储
│   └── config.ts         # 生图参数配置
├── scripts/               # 脚本文件
│   └── init-db.js        # 数据库初始化
└── types/                 # TypeScript类型定义
\`\`\`

## API接口说明

### 生图接口
\`\`\`
POST /api/generate
Content-Type: application/json

{
  "prompt": "生图提示词",
  "model": "flux|turbo|stabilityai",
  "aspectRatio": "1:1|3:2|2:3"
}
\`\`\`

**生图参数配置**：
- `model`: 用户选择的模型名称（flux, turbo, stabilityai）
- `seed`: 系统自动生成随机种子
- `width/height`: 根据比例自动计算
  - 1:1 = 1024×1024
  - 3:2 = 900×600  
  - 2:3 = 600×900
- `nologo`: true（不显示logo）
- `private`: true（私有生成）
- `enhance`: true（增强提示词）
- `safe`: true（安全过滤）
- `referrer`: freeai.yooart.top
- `token`: 从环境变量 `POLLINATIONS_TOKEN` 读取（可选）

### 管理后台接口
- \`POST /api/admin/login\` - 管理员登录
- \`POST /api/admin/logout\` - 管理员退出
- \`GET /api/admin/stats\` - 获取统计数据
- \`GET /api/admin/records\` - 获取创作记录

## 生产部署

### 1. 构建项目
\`\`\`bash
pnpm build
\`\`\`

### 2. 启动生产服务器
\`\`\`bash
pnpm start
\`\`\`

### 3. 注意事项
- 确保 \`.env.local\` 中的 \`JWT_SECRET\` 足够安全
- 修改默认的管理员账号密码
- 如果使用反向代理，确保正确传递 IP 地址

## 常见问题

### Q: 数据库连接问题怎么办？
A: 项目已使用 @libsql/client 替代 better-sqlite3，解决了Windows编译问题。如果仍有问题，请检查数据库文件权限。

### Q: 如何修改生图模型？
A: 编辑 \`lib/config.ts\` 中的 \`IMAGE_GENERATION_CONFIG.models\` 数组

### Q: 如何修改尺寸比例和参数？
A: 在 \`lib/config.ts\` 中的 \`IMAGE_GENERATION_CONFIG\` 对象中修改：
- \`aspectRatios\`: 修改尺寸比例和具体尺寸
- \`fixedParams\`: 修改固定参数（nologo, private, enhance等）
- \`getDynamicParams\`: 修改动态参数（如token）

### Q: 如何自定义管理后台？
A: 管理后台的组件在 \`app/admin/\` 和 \`components/admin-layout.tsx\` 中

## 许可证

MIT License

## 贡献

欢迎提交 Issues 和 Pull Requests！ 