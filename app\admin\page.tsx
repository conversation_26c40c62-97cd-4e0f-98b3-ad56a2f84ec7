"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import AdminLayout from "@/components/admin-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts'
import { TrendingUp, Image as ImageIcon, Users, Clock } from "lucide-react"

interface StatsData {
  totalCount: number
  dailyStats: Array<{
    date: string
    count: number
  }>
  modelStats: Array<{
    model: string
    count: number
  }>
}

export default function AdminDashboard() {
  const [stats, setStats] = useState<StatsData | null>(null)
  const [loading, setLoading] = useState(true)
  const router = useRouter()

  useEffect(() => {
    const fetchStats = async () => {
      try {
        const response = await fetch('/api/admin/stats')
        
        if (response.status === 401) {
          router.push('/admin/login')
          return
        }
        
        if (!response.ok) {
          throw new Error('获取统计数据失败')
        }
        
        const result = await response.json()
        if (result.success) {
          setStats(result.data)
        }
      } catch (error) {
        console.error('获取统计数据错误:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchStats()
  }, [router])

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-gray-500">加载中...</div>
        </div>
      </AdminLayout>
    )
  }

  const formatChartData = (dailyStats: StatsData['dailyStats']) => {
    return dailyStats.map(item => ({
      date: new Date(item.date).toLocaleDateString('zh-CN', { 
        month: 'short', 
        day: 'numeric' 
      }),
      count: item.count
    }))
  }

  return (
    <AdminLayout>
      <div className="px-4 sm:px-0">
        <div className="mb-8">
          <h1 className="text-2xl font-bold text-gray-900">数据概览</h1>
          <p className="mt-2 text-gray-600">查看生图服务的使用统计和趋势</p>
        </div>

        {/* 统计卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">总生图数量</CardTitle>
              <ImageIcon className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats?.totalCount || 0}</div>
              <p className="text-xs text-muted-foreground">累计生成图片数量</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">今日生图</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {stats?.dailyStats?.[stats.dailyStats.length - 1]?.count || 0}
              </div>
              <p className="text-xs text-muted-foreground">今天生成的图片数量</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">热门模型</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {stats?.modelStats?.[0]?.model || 'N/A'}
              </div>
              <p className="text-xs text-muted-foreground">
                使用次数: {stats?.modelStats?.[0]?.count || 0}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">7日平均</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {stats?.dailyStats ? 
                  Math.round(stats.dailyStats.slice(-7).reduce((sum, day) => sum + day.count, 0) / 7) 
                  : 0}
              </div>
              <p className="text-xs text-muted-foreground">过去7天日均生图数</p>
            </CardContent>
          </Card>
        </div>

        {/* 图表区域 */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 每日生图趋势 */}
          <Card className="col-span-1 lg:col-span-2">
            <CardHeader>
              <CardTitle>每日生图趋势</CardTitle>
              <CardDescription>过去30天的生图数量变化</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={stats?.dailyStats ? formatChartData(stats.dailyStats) : []}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis 
                      dataKey="date" 
                      fontSize={12}
                      tickLine={false}
                      axisLine={false}
                    />
                    <YAxis 
                      fontSize={12}
                      tickLine={false}
                      axisLine={false}
                    />
                    <Tooltip />
                    <Bar 
                      dataKey="count" 
                      fill="#f26d21" 
                      radius={[4, 4, 0, 0]}
                    />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>

          {/* 模型使用统计 */}
          <Card>
            <CardHeader>
              <CardTitle>模型使用统计</CardTitle>
              <CardDescription>各生图模型的使用情况</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {stats?.modelStats?.map((model, index) => (
                  <div key={model.model} className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <div 
                        className="w-3 h-3 rounded-full" 
                        style={{ backgroundColor: `hsl(${index * 120}, 70%, 50%)` }}
                      />
                      <span className="text-sm font-medium">{model.model}</span>
                    </div>
                    <div className="text-sm text-gray-500">{model.count} 次</div>
                  </div>
                )) || (
                  <div className="text-center text-gray-500 py-4">
                    暂无数据
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </AdminLayout>
  )
} 