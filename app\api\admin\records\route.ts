import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { imageGenerations } from '@/lib/schema';
import { desc, count, like } from 'drizzle-orm';
import jwt from 'jsonwebtoken';

// 验证管理员token
function verifyAdminToken(request: NextRequest) {
  const token = request.cookies.get('admin-token')?.value;
  if (!token) {
    return false;
  }
  
  try {
    const jwtSecret = process.env.JWT_SECRET || 'default-secret';
    jwt.verify(token, jwtSecret);
    return true;
  } catch {
    return false;
  }
}

export async function GET(request: NextRequest) {
  try {
    // 验证管理员权限
    if (!verifyAdminToken(request)) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 });
    }
    
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const search = searchParams.get('search') || '';
    
    const offset = (page - 1) * limit;
    
    // 构建查询条件
    let whereClause = undefined;
    if (search) {
      whereClause = like(imageGenerations.prompt, `%${search}%`);
    }
    
    // 获取记录列表
    const records = await db
      .select()
      .from(imageGenerations)
      .where(whereClause)
      .orderBy(desc(imageGenerations.requestTime))
      .limit(limit)
      .offset(offset);
    
    // 获取总数
    const totalCountResult = await db
      .select({ count: count() })
      .from(imageGenerations)
      .where(whereClause);
    
    const totalCount = totalCountResult[0]?.count || 0;
    const totalPages = Math.ceil(totalCount / limit);
    
    return NextResponse.json({
      success: true,
      data: {
        records,
        pagination: {
          page,
          limit,
          totalCount,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1
        }
      }
    });
    
  } catch (error) {
    console.error('获取记录API错误:', error);
    return NextResponse.json(
      { error: '获取记录失败' }, 
      { status: 500 }
    );
  }
} 