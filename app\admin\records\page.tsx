"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import AdminLayout from "@/components/admin-layout"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table"
import { Search, ChevronLeft, ChevronRight, ExternalLink, Copy, Check } from "lucide-react"

interface ImageRecord {
  id: string
  requestIp: string
  prompt: string
  imageUrl: string
  model: string
  generationTime: number
  requestTime: string
  createdAt: string
}

interface RecordsData {
  records: ImageRecord[]
  pagination: {
    page: number
    limit: number
    totalCount: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

export default function AdminRecords() {
  const [data, setData] = useState<RecordsData | null>(null)
  const [loading, setLoading] = useState(true)
  const [search, setSearch] = useState("")
  const [currentPage, setCurrentPage] = useState(1)
  const [copiedUrl, setCopiedUrl] = useState<string | null>(null)
  const router = useRouter()

  const fetchRecords = async (page: number, searchTerm: string = "") => {
    setLoading(true)
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: "20",
        ...(searchTerm && { search: searchTerm })
      })
      
      const response = await fetch(`/api/admin/records?${params}`)
      
      if (response.status === 401) {
        router.push('/admin/login')
        return
      }
      
      if (!response.ok) {
        throw new Error('获取记录失败')
      }
      
      const result = await response.json()
      if (result.success) {
        setData(result.data)
      }
    } catch (error) {
      console.error('获取记录错误:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchRecords(currentPage, search)
  }, [currentPage, router])

  const handleSearch = () => {
    setCurrentPage(1)
    fetchRecords(1, search)
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch()
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN')
  }

  const formatDuration = (seconds: number) => {
    return `${seconds.toFixed(2)}s`
  }

  const truncateText = (text: string, maxLength: number = 50) => {
    return text.length > maxLength ? text.substring(0, maxLength) + '...' : text
  }

  const truncateUrl = (url: string, maxLength: number = 30) => {
    // 如果是Pollinations.ai的链接，可以进一步简化显示
    if (url.includes('image.pollinations.ai')) {
      const shortUrl = url.replace('https://image.pollinations.ai/prompt/', '.../')
      return shortUrl.length > maxLength ? shortUrl.substring(0, maxLength) + '...' : shortUrl
    }
    return url.length > maxLength ? url.substring(0, maxLength) + '...' : url
  }

  const copyToClipboard = async (text: string, id: string) => {
    try {
      await navigator.clipboard.writeText(text)
      setCopiedUrl(id)
      setTimeout(() => setCopiedUrl(null), 2000)
    } catch (error) {
      console.error('复制失败:', error)
    }
  }

  return (
    <AdminLayout>
      <div className="px-4 sm:px-0">
        <div className="mb-8">
          <h1 className="text-2xl font-bold text-gray-900">创作记录</h1>
          <p className="mt-2 text-gray-600">查看所有用户的生图记录</p>
        </div>

        {/* 搜索框 */}
        <Card className="mb-6">
          <CardContent className="pt-6">
            <div className="flex gap-4">
              <div className="flex-1">
                <Input
                  placeholder="搜索提示词..."
                  value={search}
                  onChange={(e) => setSearch(e.target.value)}
                  onKeyPress={handleKeyPress}
                />
              </div>
              <Button onClick={handleSearch} className="px-6">
                <Search className="w-4 h-4 mr-2" />
                搜索
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* 记录表格 */}
        <Card>
          <CardHeader>
            <CardTitle>
              生图记录 
              {data && (
                <span className="text-sm font-normal text-gray-500 ml-2">
                  (共 {data.pagination.totalCount} 条记录)
                </span>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="flex items-center justify-center h-32">
                <div className="text-gray-500">加载中...</div>
              </div>
            ) : (
              <>
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>ID</TableHead>
                        <TableHead>请求IP</TableHead>
                        <TableHead>提示词</TableHead>
                        <TableHead>模型</TableHead>
                        <TableHead>生图时长</TableHead>
                        <TableHead>请求时间</TableHead>
                        <TableHead>图片链接</TableHead>
                        <TableHead>操作</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {data?.records.map((record) => (
                        <TableRow key={record.id}>
                          <TableCell className="font-mono text-xs">
                            {record.id.substring(0, 8)}...
                          </TableCell>
                          <TableCell>{record.requestIp}</TableCell>
                          <TableCell>
                            <div 
                              className="max-w-xs"
                              title={record.prompt}
                            >
                              {truncateText(record.prompt)}
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline">{record.model}</Badge>
                          </TableCell>
                          <TableCell>
                            {formatDuration(record.generationTime)}
                          </TableCell>
                          <TableCell className="text-sm">
                            {formatDate(record.requestTime)}
                          </TableCell>
                          <TableCell>
                            <div
                              className="max-w-xs font-mono text-xs text-blue-600 cursor-pointer hover:text-blue-800"
                              title={record.imageUrl}
                              onClick={() => copyToClipboard(record.imageUrl, record.id)}
                            >
                              {truncateUrl(record.imageUrl)}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex gap-1">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => copyToClipboard(record.imageUrl, record.id)}
                                title="复制链接"
                              >
                                {copiedUrl === record.id ? (
                                  <Check className="w-4 h-4 text-green-600" />
                                ) : (
                                  <Copy className="w-4 h-4" />
                                )}
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => window.open(record.imageUrl, '_blank')}
                                title="打开链接"
                              >
                                <ExternalLink className="w-4 h-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      )) || (
                        <TableRow>
                          <TableCell colSpan={8} className="text-center py-8">
                            暂无记录
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </div>

                {/* 分页 */}
                {data && data.pagination.totalPages > 1 && (
                  <div className="flex items-center justify-between mt-6">
                    <div className="text-sm text-gray-500">
                      第 {data.pagination.page} 页，共 {data.pagination.totalPages} 页
                    </div>
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setCurrentPage(prev => prev - 1)}
                        disabled={!data.pagination.hasPrev}
                      >
                        <ChevronLeft className="w-4 h-4 mr-1" />
                        上一页
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setCurrentPage(prev => prev + 1)}
                        disabled={!data.pagination.hasNext}
                      >
                        下一页
                        <ChevronRight className="w-4 h-4 ml-1" />
                      </Button>
                    </div>
                  </div>
                )}
              </>
            )}
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  )
} 