# 生图URL示例

根据用户输入的参数，系统会自动生成包含完整参数的Pollinations.AI请求URL。

## 示例

### 用户输入
```json
{
  "prompt": "A beautiful sunset over the ocean",
  "model": "flux",
  "aspectRatio": "3:2"
}
```

### 系统生成的完整URL
```
https://image.pollinations.ai/prompt/A%20beautiful%20sunset%20over%20the%20ocean?model=flux&seed=123456&width=900&height=600&nologo=true&private=true&enhance=true&safe=true&referrer=freeai.yooart.top&token=YOUR_TOKEN
```

### 参数说明
- `prompt`: URL编码后的提示词
- `model`: flux（用户选择）
- `seed`: 123456（系统随机生成）
- `width`: 900（根据3:2比例计算）
- `height`: 600（根据3:2比例计算）
- `nologo`: true（固定参数）
- `private`: true（固定参数）
- `enhance`: true（固定参数）
- `safe`: true（固定参数）
- `referrer`: freeai.yooart.top（固定参数）
- `token`: YOUR_TOKEN（环境变量配置，可选）

## 所有支持的比例

| 比例 | 尺寸 | 说明 |
|------|------|------|
| 1:1 | 1024×1024 | 正方形 |
| 3:2 | 900×600 | 横屏 |
| 2:3 | 600×900 | 竖屏 |

## 配置修改

所有参数都可以在 `lib/config.ts` 中进行修改，包括：
- 新增尺寸比例
- 修改固定参数值
- 添加新的动态参数 