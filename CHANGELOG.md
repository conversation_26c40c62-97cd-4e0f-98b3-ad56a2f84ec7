# 更新日志

## v1.1.0 - 2024-01-XX

### 🔧 重要技术更新

#### 数据库驱动更换
- ✅ 将 `better-sqlite3` 替换为 `@libsql/client`
- ✅ 解决了 Windows 上的编译问题
- ✅ 改进了跨平台兼容性
- ✅ 保持了完全的 SQLite 兼容性

### ✨ 新功能

#### 完整的Pollinations.AI参数支持
- ✅ 添加了所有Pollinations.AI官方参数支持
- ✅ 支持自动生成随机种子
- ✅ 智能尺寸比例计算
- ✅ 完整的参数配置化管理

#### 新增参数
- `model`: 用户选择的生图模型
- `seed`: 系统自动生成随机种子
- `width/height`: 根据比例自动计算尺寸
- `nologo`: true（隐藏Pollinations logo）
- `private`: true（私有生成）
- `enhance`: true（AI增强提示词）
- `safe`: true（内容安全过滤）
- `referrer`: freeai.yooart.top（推荐方标识）
- `token`: 支持API token认证（可选）

#### 尺寸比例配置
| 比例 | 尺寸 | 用途 |
|------|------|------|
| 1:1 | 1024×1024 | 正方形图片 |
| 3:2 | 900×600 | 横屏壁纸 |
| 2:3 | 600×900 | 竖屏手机壁纸 |

### 🔧 技术改进

#### 新增配置文件
- `lib/config.ts`: 集中管理生图参数配置
  - `IMAGE_GENERATION_CONFIG`: 主配置对象
  - `generateRandomSeed()`: 随机种子生成
  - `getDimensionsByRatio()`: 比例转尺寸
  - `buildImageUrl()`: URL构建器

#### 参数化设计
- ✅ 所有固定参数可在配置文件中修改
- ✅ 支持从环境变量读取动态参数
- ✅ 便于后期维护和扩展

#### API优化
- 简化前端参数传递，只需要 `prompt`、`model`、`aspectRatio`
- 后端自动处理尺寸计算和参数组装
- 完整的URL构建和验证

### 📁 文件变更

#### 新增文件
- `lib/config.ts` - 生图配置管理
- `DEMO.md` - URL示例演示
- `CHANGELOG.md` - 更新日志

#### 修改文件
- `lib/db.ts` - 更换为 @libsql/client 数据库驱动
- `drizzle.config.ts` - 更新数据库配置为 turso dialect
- `scripts/init-db.js` - 使用异步 LibSQL 客户端
- `app/api/generate/route.ts` - 使用新的配置系统
- `components/prompt-input.tsx` - 使用配置化的模型和比例
- `app/page.tsx` - 简化API调用参数
- `README.md` - 更新文档和配置说明

### 🌐 环境变量

新增可选环境变量：
```env
# Pollinations.AI API Token (可选)
POLLINATIONS_TOKEN=YOUR_TOKEN
```

### 📊 API变更

#### 生图接口更新
```json
// 旧版本
{
  "prompt": "提示词",
  "model": "flux",
  "width": 1024,
  "height": 1024
}

// 新版本（简化）
{
  "prompt": "提示词", 
  "model": "flux",
  "aspectRatio": "1:1"
}
```

#### 生成的URL示例
```
https://image.pollinations.ai/prompt/A%20beautiful%20sunset%20over%20the%20ocean?model=flux&seed=123456&width=900&height=600&nologo=true&private=true&enhance=true&safe=true&referrer=freeai.yooart.top&token=YOUR_TOKEN
```

### 🎯 下一步计划

- [ ] 添加更多尺寸比例选项
- [ ] 支持自定义种子输入
- [ ] 添加批量生图功能
- [ ] 优化图片加载和缓存 