"use client"

import { useState, useEffect } from "react"
import { Navbar } from "@/components/navbar"
import PromptInput from "@/components/prompt-input"
import ImageGrid from "@/components/image-grid"
import ImagePreview from "@/components/image-preview"
import { getAllImageRecords } from "@/lib/indexeddb"
import type { Image } from "@/types/image"
import { toast, Toaster } from "sonner"

// 模拟数据
const mockImages: Image[] = [
  {
    id: "1",
    url: "/placeholder.svg?height=800&width=600&text=城市夜景",
    prompt: "一座现代化城市的夜景，霓虹灯闪烁，高楼大厦，车流如织，星空璀璨",
    model: "Flux",
    createdAt: new Date().toISOString(),
    aspectRatio: "3:4",
  },
  {
    id: "2",
    url: "/placeholder.svg?height=800&width=600&text=森林小屋",
    prompt:
      "一个穿着冬季外套和毛线帽的男孩在宁静的松树林中仰望红色灯笼，阳光透过树木洒落，氛围宁静祥和，吉卜力风格动漫背景",
    model: "Flux",
    createdAt: new Date().toISOString(),
    aspectRatio: "3:4",
  },
  {
    id: "3",
    url: "/placeholder.svg?height=800&width=600&text=海边日落",
    prompt: "海边日落，金色阳光洒在海面上，远处有帆船剪影，天空呈现渐变色彩",
    model: "Stable Diffusion",
    createdAt: new Date().toISOString(),
    aspectRatio: "3:4",
  },
  {
    id: "4",
    url: "/placeholder.svg?height=800&width=600&text=花园场景",
    prompt: "一个充满鲜花的英式花园，蝴蝶飞舞，阳光明媚，远处有一座古典建筑",
    model: "DALL-E",
    createdAt: new Date().toISOString(),
    aspectRatio: "3:4",
  },
]

export default function Home() {
  const [images, setImages] = useState<Image[]>([])
  const [selectedImage, setSelectedImage] = useState<Image | null>(null)
  const [isGenerating, setIsGenerating] = useState(false)

  // 初始化时加载IndexedDB中的数据
  useEffect(() => {
    const loadImages = async () => {
      try {
        const records = await getAllImageRecords()
        const convertedImages: Image[] = records.map(record => ({
          id: record.id,
          url: record.imageUrl || '/placeholder.svg?height=800&width=600&text=生成中...',
          prompt: record.prompt,
          model: record.model,
          createdAt: new Date(record.requestTime).toISOString(),
          aspectRatio: '1:1', // 默认比例
        }))
        setImages(convertedImages)
      } catch (error) {
        console.error('加载本地图片失败:', error)
        // 如果加载失败，显示模拟数据
        setImages(mockImages)
      }
    }
    
    loadImages()
  }, [])

  const handleGenerateImage = async (prompt: string, aspectRatio: string, model: string) => {
    setIsGenerating(true)
    
    try {
      // 调用后端API生成图片（尺寸计算已移到后端）
      const response = await fetch('/api/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt,
          model,
          aspectRatio,
        }),
      })
      
      const result = await response.json()
      
      if (!response.ok) {
        throw new Error(result.error || '生图失败')
      }
      
      if (result.success) {
        // 创建新图片对象
        const newImage: Image = {
          id: result.data.id,
          url: result.data.imageUrl,
          prompt: result.data.prompt,
          model: result.data.model,
          createdAt: new Date(result.data.requestTime).toISOString(),
          aspectRatio,
        }
        
        // 添加到图片列表顶部
        setImages((prev) => [newImage, ...prev])
        
        toast.success('图片生成成功！')
      }
      
    } catch (error) {
      console.error('生图失败:', error)
      toast.error(error instanceof Error ? error.message : '生图失败，请稍后重试')
    } finally {
      setIsGenerating(false)
    }
  }

  const handleDeleteImage = (id: string) => {
    setImages((prev) => prev.filter((img) => img.id !== id))
    if (selectedImage?.id === id) {
      setSelectedImage(null)
    }
  }

  const handleImageClick = (image: Image) => {
    setSelectedImage(image)
  }

  const handleClosePreview = () => {
    setSelectedImage(null)
  }

  return (
    <main className="min-h-screen dot-grid-bg-subtle">
      <Navbar />

      <div className="max-w-[1200px] mx-auto px-4 pt-24 pb-8 md:pb-16">
        <div className="text-center mb-8">
          <h2 className="text-2xl md:text-3xl font-medium text-gray-700">Hey，设计师</h2>
          <h1 className="text-3xl md:text-5xl font-normal text-[#f26d21] mt-4">今天想画点什么？</h1>
        </div>

        <PromptInput onGenerate={handleGenerateImage} isGenerating={isGenerating} />

        {images.length > 0 && (
          <ImageGrid images={images} onImageClick={handleImageClick} onDeleteImage={handleDeleteImage} />
        )}
      </div>

      {selectedImage && <ImagePreview image={selectedImage} onClose={handleClosePreview} />}
      
      <Toaster position="top-right" />
    </main>
  )
}
