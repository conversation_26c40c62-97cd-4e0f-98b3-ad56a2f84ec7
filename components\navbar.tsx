"use client"

import Link from "next/link"
import Image from "next/image"
import { ChevronRight } from "lucide-react"
import { RainbowButton } from "@/components/magicui/rainbow-button"
import { usePathname } from "next/navigation"

export function Navbar() {
  const pathname = usePathname()

  return (
    <header className="fixed top-0 left-0 right-0 z-50 bg-white/10 backdrop-blur-[20px]">
      <div className="max-w-[1200px] mx-auto px-4 py-4 flex items-center justify-between">
        <Link href="/" className="flex items-center">
          <Image
            src="/logo.png?height=36&width=36"
            alt="YOOART Logo"
            width={32}
            height={32}
            className="mr-2"
          />
          <span className="font-bold text-xl">YOOART</span>
        </Link>

        <nav className="flex space-x-6 absolute left-1/2 transform -translate-x-1/2">
          <a
            href="https://yooart.top" target="_blank"
            className="transition-colors text-black/50 hover:text-black/70"
          >
            首页
          </a>
          <span className="font-bold text-black">
            免费画图
          </span>
          <a
            href="https://yooart.top/gallery" target="_blank"
            className="transition-colors text-black/50 hover:text-black/70"
          >
            灵感画廊
          </a>
        </nav>

        <a href="http://chat.yooart.top" target="_blank" rel="noopener noreferrer">
          <RainbowButton className="flex items-center gap-2">
            开始画图
            <ChevronRight size={12} />
          </RainbowButton>
        </a>
      </div>
    </header>
  )
}
