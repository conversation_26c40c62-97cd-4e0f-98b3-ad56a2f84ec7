const { createClient } = require('@libsql/client');
const path = require('path');

// 数据库配置
const dbUrl = process.env.DATABASE_URL || 'file:./sqlite.db';

console.log('初始化数据库...');
console.log('数据库URL:', dbUrl);

async function initDatabase() {
  try {
    // 创建数据库连接
    const client = createClient({
      url: dbUrl,
    });
    
    // 创建表
    const createTableSQL = `
      CREATE TABLE IF NOT EXISTS image_generations (
        id TEXT PRIMARY KEY,
        request_ip TEXT NOT NULL,
        prompt TEXT NOT NULL,
        image_url TEXT NOT NULL,
        model TEXT NOT NULL,
        generation_time REAL NOT NULL,
        request_time INTEGER NOT NULL,
        created_at INTEGER NOT NULL
      );
    `;
    
    await client.execute(createTableSQL);
    
    // 创建索引
    await client.execute('CREATE INDEX IF NOT EXISTS idx_image_generations_request_time ON image_generations(request_time);');
    await client.execute('CREATE INDEX IF NOT EXISTS idx_image_generations_model ON image_generations(model);');
    await client.execute('CREATE INDEX IF NOT EXISTS idx_image_generations_created_at ON image_generations(created_at);');
    
    console.log('✅ 数据库初始化完成！');
    console.log('📊 表结构已创建：image_generations');
    console.log('🔍 索引已创建：request_time, model, created_at');
    
    await client.close();
    
  } catch (error) {
    console.error('❌ 数据库初始化失败:', error);
    process.exit(1);
  }
}

initDatabase(); 