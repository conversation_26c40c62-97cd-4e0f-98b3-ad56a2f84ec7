import { sqliteTable, text, integer, real } from 'drizzle-orm/sqlite-core';
import { sql } from 'drizzle-orm';

export const imageGenerations = sqliteTable('image_generations', {
  id: text('id').primaryKey(),
  requestIp: text('request_ip').notNull(),
  prompt: text('prompt').notNull(),
  imageUrl: text('image_url').notNull(), // 包含完整参数的图片链接
  model: text('model').notNull(),
  generationTime: real('generation_time').notNull(), // 生图时长（秒）
  requestTime: integer('request_time', { mode: 'timestamp' }).default(sql`(unixepoch())`).notNull(),
  createdAt: integer('created_at', { mode: 'timestamp' }).default(sql`(unixepoch())`).notNull(),
}); 