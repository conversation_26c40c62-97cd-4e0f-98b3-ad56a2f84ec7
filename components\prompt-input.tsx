"use client"

import { useState, useRef, useEffect } from "react"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Loader2 } from "lucide-react"
import { addImageRecord } from "@/lib/indexeddb"
import { IMAGE_GENERATION_CONFIG } from "@/lib/config"
import { v4 as uuidv4 } from 'uuid'

interface PromptInputProps {
  onGenerate: (prompt: string, aspectRatio: string, model: string) => void
  isGenerating?: boolean
}

export default function PromptInput({ onGenerate, isGenerating = false }: PromptInputProps) {
  const [prompt, setPrompt] = useState("")
  const [aspectRatio, setAspectRatio] = useState("1:1")
  const [model, setModel] = useState("flux")
  const [isModelMenuOpen, setIsModelMenuOpen] = useState(false)
  const textareaRef = useRef<HTMLTextAreaElement>(null)

  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = "auto"
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`
    }
  }, [prompt])

  const handleSubmit = async () => {
    if (prompt.trim() && !isGenerating) {
      try {
        const startTime = Date.now();
        
        // 调用生图功能
        await onGenerate(prompt, aspectRatio, model)
        
        // 存储到IndexedDB
        const endTime = Date.now();
        const generationTime = endTime - startTime;
        
        await addImageRecord({
          id: uuidv4(),
          prompt: prompt.trim(),
          model,
          generationTime,
          requestTime: startTime,
        });
        
        setPrompt("")
      } catch (error) {
        console.error('生图失败:', error);
      }
    }
  }

  // 使用配置文件中的数据
  const aspectRatios = IMAGE_GENERATION_CONFIG.aspectRatios
  const models = IMAGE_GENERATION_CONFIG.models

  return (
    <div className="max-w-[880px] mx-auto mb-12">
      <div className="bg-white rounded-2xl shadow-sm">
        <textarea
          ref={textareaRef}
          value={prompt}
          onChange={(e) => setPrompt(e.target.value)}
          placeholder="输入你的生图提示词"
          className="w-full p-4 min-h-[128px] max-h-[600px] overflow-y-auto resize-none focus:outline-none rounded-2xl"
        />

        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between px-4 pb-4 pt-2">
          <div className="flex items-center space-x-4 mb-3 sm:mb-0">
            <div className="flex space-x-2">
              {aspectRatios.map((ratio) => (
                <button
                  key={ratio.value}
                  onClick={() => setAspectRatio(ratio.value)}
                  className={`px-3 py-1 rounded-full text-sm ${
                    aspectRatio === ratio.value ? "bg-gray-200 font-medium" : "bg-gray-100 text-gray-600"
                  }`}
                >
                  {ratio.label}
                </button>
              ))}
            </div>

            <div className="relative">
              <button
                onClick={() => setIsModelMenuOpen(!isModelMenuOpen)}
                className="flex items-center space-x-1 px-3 py-1 rounded-full bg-gray-100 text-sm"
              >
                <Sparkles className="h-3 w-3 mr-1" />
                <span>{models.find(m => m.value === model)?.label || model}</span>
                <svg width="12" height="12" viewBox="0 0 12 12">
                  <path fill="currentColor" d="M6 8.5l4-4 1 1-5 5-5-5 1-1z" />
                </svg>
              </button>

              {isModelMenuOpen && (
                <div className="absolute top-full left-0 mt-1 bg-white shadow-lg rounded-lg py-1 z-10 w-40">
                  {models.map((m) => (
                    <button
                      key={m.value}
                      onClick={() => {
                        setModel(m.value)
                        setIsModelMenuOpen(false)
                      }}
                      className={`block w-full text-left px-3 py-2 text-sm ${
                        model === m.value ? "bg-gray-100" : "hover:bg-gray-50"
                      }`}
                    >
                      {m.label}
                    </button>
                  ))}
                </div>
              )}
            </div>
          </div>

          <button
            onClick={handleSubmit}
            disabled={isGenerating || !prompt.trim()}
            className="bg-black text-white hover:bg-gray-800 disabled:bg-gray-400 rounded-xl p-2 w-[36px] h-[36px] flex items-center justify-center mr-0"
            aria-label={isGenerating ? "生成中..." : "提交"}
          >
            {isGenerating ? (
              <Loader2 className="w-[22px] h-[22px] animate-spin" />
            ) : (
              <ArrowUp className="w-[22px] h-[22px]" />
            )}
          </button>
        </div>
      </div>
    </div>
  )
}
