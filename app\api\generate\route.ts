import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { imageGenerations } from '@/lib/schema';
import { v4 as uuidv4 } from 'uuid';
import { buildImageUrl, generateRandomSeed, getDimensionsByRatio } from '@/lib/config';

export async function POST(request: NextRequest) {
  try {
    let body;
    try {
      body = await request.json();
    } catch (parseError) {
      console.error('JSON解析错误:', parseError);
      return NextResponse.json({ error: '请求格式错误，请确保发送有效的JSON数据' }, { status: 400 });
    }

    const { prompt, model = 'flux', aspectRatio = '1:1' } = body;
    
    if (!prompt) {
      return NextResponse.json({ error: '提示词不能为空' }, { status: 400 });
    }

    // 获取客户端IP（优先获取公网IP）
    const getClientIP = () => {
      // 尝试从各种header中获取真实IP
      const forwardedFor = request.headers.get('x-forwarded-for');
      const realIP = request.headers.get('x-real-ip');
      const cfConnectingIP = request.headers.get('cf-connecting-ip'); // Cloudflare
      const xClientIP = request.headers.get('x-client-ip');
      const xClusterClientIP = request.headers.get('x-cluster-client-ip');

      // x-forwarded-for 可能包含多个IP，取第一个
      if (forwardedFor) {
        const ips = forwardedFor.split(',').map(ip => ip.trim());
        const publicIP = ips.find(ip => {
          // 过滤掉私有IP地址
          return !ip.startsWith('10.') &&
                 !ip.startsWith('172.') &&
                 !ip.startsWith('192.168.') &&
                 !ip.startsWith('127.') &&
                 ip !== '::1';
        });
        if (publicIP) return publicIP;
        return ips[0]; // 如果没有公网IP，返回第一个
      }

      // 按优先级返回其他header中的IP
      return cfConnectingIP || realIP || xClientIP || xClusterClientIP || '127.0.0.1';
    };

    const ip = getClientIP();

    const startTime = Date.now();
    
    // 生成随机种子
    const seed = generateRandomSeed();
    
    // 根据比例获取尺寸
    const { width, height } = getDimensionsByRatio(aspectRatio);
    
    // 构建完整的图片生成URL（包含所有参数）
    const imageUrl = buildImageUrl(prompt, {
      model,
      seed,
      width,
      height,
    });

    console.log('生成的图片URL:', imageUrl);

    try {
      // 实际调用Pollinations.ai API生成图片
      const imageResponse = await fetch(imageUrl, {
        method: 'GET',
        headers: {
          'User-Agent': 'YOOART-FreeAI/1.0',
        },
        // 增加超时时间，因为图片生成可能需要较长时间
        signal: AbortSignal.timeout(60000), // 60秒超时
      });

      if (!imageResponse.ok) {
        throw new Error(`图片生成失败: ${imageResponse.status} ${imageResponse.statusText}`);
      }

      // 检查响应是否为图片
      const contentType = imageResponse.headers.get('content-type');
      if (!contentType || !contentType.startsWith('image/')) {
        const responseText = await imageResponse.text();
        console.error('非图片响应:', responseText);
        throw new Error('外部服务返回了非图片内容');
      }

      const endTime = Date.now();
      const generationTime = (endTime - startTime) / 1000; // 转换为秒

      // 保存到数据库
      const id = uuidv4();
      await db.insert(imageGenerations).values({
        id,
        requestIp: ip,
        prompt,
        imageUrl,
        model,
        generationTime,
        requestTime: new Date(endTime),
        createdAt: new Date(endTime),
      });

      return NextResponse.json({
        success: true,
        data: {
          id,
          imageUrl,
          prompt,
          model,
          generationTime,
          requestTime: endTime,
        }
      });

    } catch (fetchError) {
      console.error('调用外部图片生成API失败:', fetchError);

      // 根据错误类型返回不同的错误信息
      if (fetchError instanceof Error) {
        if (fetchError.name === 'TimeoutError') {
          throw new Error('图片生成超时，请稍后重试');
        } else if (fetchError.message.includes('fetch')) {
          throw new Error('无法连接到图片生成服务');
        } else {
          throw new Error(fetchError.message);
        }
      }

      throw new Error('图片生成服务暂时不可用');
    }

  } catch (error) {
    console.error('生图API错误:', error);
    
    // 更详细的错误信息
    let errorMessage = '生图失败，请稍后重试';
    if (error instanceof Error) {
      console.error('错误详情:', error.message);
      console.error('错误堆栈:', error.stack);
      
      // 如果是JSON解析错误，提供更具体的提示
      if (error.message.includes('JSON') || error.message.includes('DOCTYPE')) {
        errorMessage = '外部图片生成服务暂时不可用，请稍后重试';
      }
    }
    
    return NextResponse.json(
      { error: errorMessage }, 
      { status: 500 }
    );
  }
} 