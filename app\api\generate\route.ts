import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { imageGenerations } from '@/lib/schema';
import { v4 as uuidv4 } from 'uuid';
import { buildImageUrl, generateRandomSeed, getDimensionsByRatio } from '@/lib/config';

export async function POST(request: NextRequest) {
  try {
    let body;
    try {
      body = await request.json();
    } catch (parseError) {
      console.error('JSON解析错误:', parseError);
      return NextResponse.json({ error: '请求格式错误，请确保发送有效的JSON数据' }, { status: 400 });
    }

    const { prompt, model = 'flux', aspectRatio = '1:1' } = body;
    
    if (!prompt) {
      return NextResponse.json({ error: '提示词不能为空' }, { status: 400 });
    }

    // 获取客户端IP
    const ip = request.headers.get('x-forwarded-for') || 
               request.headers.get('x-real-ip') || 
               '127.0.0.1';

    const startTime = Date.now();
    
    // 生成随机种子
    const seed = generateRandomSeed();
    
    // 根据比例获取尺寸
    const { width, height } = getDimensionsByRatio(aspectRatio);
    
    // 构建完整的图片生成URL（包含所有参数）
    const imageUrl = buildImageUrl(prompt, {
      model,
      seed,
      width,
      height,
    });
    
    console.log('生成的图片URL:', imageUrl);
    
    const endTime = Date.now();
    const generationTime = (endTime - startTime) / 1000; // 转换为秒
    
    // 保存到数据库
    const id = uuidv4();
    await db.insert(imageGenerations).values({
      id,
      requestIp: ip,
      prompt,
      imageUrl,
      model,
      generationTime,
      requestTime: endTime, // 使用时间戳
      createdAt: endTime,   // 使用时间戳
    });

    return NextResponse.json({
      success: true,
      data: {
        id,
        imageUrl,
        prompt,
        model,
        generationTime,
        requestTime: endTime,
      }
    });

  } catch (error) {
    console.error('生图API错误:', error);
    
    // 更详细的错误信息
    let errorMessage = '生图失败，请稍后重试';
    if (error instanceof Error) {
      console.error('错误详情:', error.message);
      console.error('错误堆栈:', error.stack);
      
      // 如果是JSON解析错误，提供更具体的提示
      if (error.message.includes('JSON') || error.message.includes('DOCTYPE')) {
        errorMessage = '外部图片生成服务暂时不可用，请稍后重试';
      }
    }
    
    return NextResponse.json(
      { error: errorMessage }, 
      { status: 500 }
    );
  }
} 