// IndexedDB 工具函数
export interface ImageRecord {
  id: string;
  prompt: string;
  model: string;
  generationTime: number; // 生图时长（毫秒）
  requestTime: number; // 请求时间戳
  imageUrl?: string; // 可选的图片URL（用于向后兼容）
  imageBlob?: Blob; // 图片blob对象
  aspectRatio?: string; // 图片比例
}

const DB_NAME = 'yooart-freeai';
const DB_VERSION = 2; // 增加版本号以支持新的schema
const STORE_NAME = 'image-list';
const BLOB_STORE_NAME = 'image-blobs'; // 新增专门存储blob的store

// 打开 IndexedDB
function openDB(): Promise<IDBDatabase> {
  return new Promise((resolve, reject) => {
    const request = indexedDB.open(DB_NAME, DB_VERSION);

    request.onerror = () => reject(request.error);
    request.onsuccess = () => resolve(request.result);

    request.onupgradeneeded = (event) => {
      const db = request.result;
      const oldVersion = (event as IDBVersionChangeEvent).oldVersion;

      // 创建主要的图片记录store
      if (!db.objectStoreNames.contains(STORE_NAME)) {
        const store = db.createObjectStore(STORE_NAME, { keyPath: 'id' });
        store.createIndex('requestTime', 'requestTime', { unique: false });
        store.createIndex('model', 'model', { unique: false });
      }

      // 创建blob存储store（版本2新增）
      if (oldVersion < 2 && !db.objectStoreNames.contains(BLOB_STORE_NAME)) {
        const blobStore = db.createObjectStore(BLOB_STORE_NAME, { keyPath: 'id' });
        blobStore.createIndex('imageId', 'imageId', { unique: true });
      }
    };
  });
}

// 添加图片记录
export async function addImageRecord(record: ImageRecord): Promise<void> {
  const db = await openDB();
  const transaction = db.transaction([STORE_NAME], 'readwrite');
  const store = transaction.objectStore(STORE_NAME);
  store.add(record);
  return new Promise((resolve, reject) => {
    transaction.oncomplete = () => resolve();
    transaction.onerror = () => reject(transaction.error);
  });
}

// 获取所有图片记录
export async function getAllImageRecords(): Promise<ImageRecord[]> {
  const db = await openDB();
  const transaction = db.transaction([STORE_NAME], 'readonly');
  const store = transaction.objectStore(STORE_NAME);
  const index = store.index('requestTime');
  const request = index.getAll();
  
  return new Promise((resolve, reject) => {
    request.onsuccess = () => {
      // 按时间倒序排列
      const records = request.result.sort((a, b) => b.requestTime - a.requestTime);
      resolve(records);
    };
    request.onerror = () => reject(request.error);
  });
}

// 根据ID获取图片记录
export async function getImageRecord(id: string): Promise<ImageRecord | null> {
  const db = await openDB();
  const transaction = db.transaction([STORE_NAME], 'readonly');
  const store = transaction.objectStore(STORE_NAME);
  const request = store.get(id);
  
  return new Promise((resolve, reject) => {
    request.onsuccess = () => resolve(request.result || null);
    request.onerror = () => reject(request.error);
  });
}

// 删除图片记录
export async function deleteImageRecord(id: string): Promise<void> {
  const db = await openDB();
  const transaction = db.transaction([STORE_NAME], 'readwrite');
  const store = transaction.objectStore(STORE_NAME);
  store.delete(id);
  
  return new Promise((resolve, reject) => {
    transaction.oncomplete = () => resolve();
    transaction.onerror = () => reject(transaction.error);
  });
}

// 清空所有记录
export async function clearAllRecords(): Promise<void> {
  const db = await openDB();
  const transaction = db.transaction([STORE_NAME, BLOB_STORE_NAME], 'readwrite');
  const store = transaction.objectStore(STORE_NAME);
  const blobStore = transaction.objectStore(BLOB_STORE_NAME);
  store.clear();
  blobStore.clear();

  return new Promise((resolve, reject) => {
    transaction.oncomplete = () => resolve();
    transaction.onerror = () => reject(transaction.error);
  });
}

// 存储图片blob
export async function storeImageBlob(imageId: string, blob: Blob): Promise<void> {
  const db = await openDB();
  const transaction = db.transaction([BLOB_STORE_NAME], 'readwrite');
  const store = transaction.objectStore(BLOB_STORE_NAME);

  store.put({
    id: `blob_${imageId}`,
    imageId,
    blob,
    createdAt: Date.now()
  });

  return new Promise((resolve, reject) => {
    transaction.oncomplete = () => resolve();
    transaction.onerror = () => reject(transaction.error);
  });
}

// 获取图片blob
export async function getImageBlob(imageId: string): Promise<Blob | null> {
  const db = await openDB();
  const transaction = db.transaction([BLOB_STORE_NAME], 'readonly');
  const store = transaction.objectStore(BLOB_STORE_NAME);
  const request = store.get(`blob_${imageId}`);

  return new Promise((resolve, reject) => {
    request.onsuccess = () => {
      const result = request.result;
      resolve(result ? result.blob : null);
    };
    request.onerror = () => reject(request.error);
  });
}

// 删除图片blob
export async function deleteImageBlob(imageId: string): Promise<void> {
  const db = await openDB();
  const transaction = db.transaction([BLOB_STORE_NAME], 'readwrite');
  const store = transaction.objectStore(BLOB_STORE_NAME);
  store.delete(`blob_${imageId}`);

  return new Promise((resolve, reject) => {
    transaction.oncomplete = () => resolve();
    transaction.onerror = () => reject(transaction.error);
  });
}

// 创建blob URL（用于显示图片）
export function createBlobUrl(blob: Blob): string {
  return URL.createObjectURL(blob);
}

// 释放blob URL
export function revokeBlobUrl(url: string): void {
  URL.revokeObjectURL(url);
}