// IndexedDB 工具函数
export interface ImageRecord {
  id: string;
  prompt: string;
  model: string;
  generationTime: number; // 生图时长（毫秒）
  requestTime: number; // 请求时间戳
  imageUrl?: string; // 可选的图片URL
}

const DB_NAME = 'yooart-freeai';
const DB_VERSION = 1;
const STORE_NAME = 'image-list';

// 打开 IndexedDB
function openDB(): Promise<IDBDatabase> {
  return new Promise((resolve, reject) => {
    const request = indexedDB.open(DB_NAME, DB_VERSION);

    request.onerror = () => reject(request.error);
    request.onsuccess = () => resolve(request.result);

    request.onupgradeneeded = () => {
      const db = request.result;
      if (!db.objectStoreNames.contains(STORE_NAME)) {
        const store = db.createObjectStore(STORE_NAME, { keyPath: 'id' });
        store.createIndex('requestTime', 'requestTime', { unique: false });
        store.createIndex('model', 'model', { unique: false });
      }
    };
  });
}

// 添加图片记录
export async function addImageRecord(record: ImageRecord): Promise<void> {
  const db = await openDB();
  const transaction = db.transaction([STORE_NAME], 'readwrite');
  const store = transaction.objectStore(STORE_NAME);
  store.add(record);
  return new Promise((resolve, reject) => {
    transaction.oncomplete = () => resolve();
    transaction.onerror = () => reject(transaction.error);
  });
}

// 获取所有图片记录
export async function getAllImageRecords(): Promise<ImageRecord[]> {
  const db = await openDB();
  const transaction = db.transaction([STORE_NAME], 'readonly');
  const store = transaction.objectStore(STORE_NAME);
  const index = store.index('requestTime');
  const request = index.getAll();
  
  return new Promise((resolve, reject) => {
    request.onsuccess = () => {
      // 按时间倒序排列
      const records = request.result.sort((a, b) => b.requestTime - a.requestTime);
      resolve(records);
    };
    request.onerror = () => reject(request.error);
  });
}

// 根据ID获取图片记录
export async function getImageRecord(id: string): Promise<ImageRecord | null> {
  const db = await openDB();
  const transaction = db.transaction([STORE_NAME], 'readonly');
  const store = transaction.objectStore(STORE_NAME);
  const request = store.get(id);
  
  return new Promise((resolve, reject) => {
    request.onsuccess = () => resolve(request.result || null);
    request.onerror = () => reject(request.error);
  });
}

// 删除图片记录
export async function deleteImageRecord(id: string): Promise<void> {
  const db = await openDB();
  const transaction = db.transaction([STORE_NAME], 'readwrite');
  const store = transaction.objectStore(STORE_NAME);
  store.delete(id);
  
  return new Promise((resolve, reject) => {
    transaction.oncomplete = () => resolve();
    transaction.onerror = () => reject(transaction.error);
  });
}

// 清空所有记录
export async function clearAllRecords(): Promise<void> {
  const db = await openDB();
  const transaction = db.transaction([STORE_NAME], 'readwrite');
  const store = transaction.objectStore(STORE_NAME);
  store.clear();
  
  return new Promise((resolve, reject) => {
    transaction.oncomplete = () => resolve();
    transaction.onerror = () => reject(transaction.error);
  });
} 