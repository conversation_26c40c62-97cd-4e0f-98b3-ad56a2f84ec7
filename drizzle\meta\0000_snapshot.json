{"version": "6", "dialect": "sqlite", "id": "3ad40e17-1900-4d08-b597-5bbea4905317", "prevId": "00000000-0000-0000-0000-000000000000", "tables": {"image_generations": {"name": "image_generations", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "request_ip": {"name": "request_ip", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "prompt": {"name": "prompt", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "image_url": {"name": "image_url", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "model": {"name": "model", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "generation_time": {"name": "generation_time", "type": "real", "primaryKey": false, "notNull": true, "autoincrement": false}, "request_time": {"name": "request_time", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch())"}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(unixepoch())"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"indexes": {}}}